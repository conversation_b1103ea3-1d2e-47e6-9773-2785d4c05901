#!/usr/bin/env node

/**
 * Redis Session Cleanup Utility
 *
 * This script helps clean up orphaned and expired sessions from Redis.
 * Now includes token mismatch detection and resolution.
 *
 * Usage:
 *   node scripts/cleanup-redis-sessions.js
 *
 * Options:
 *   --dry-run    Show what would be cleaned up without actually deleting
 *   --all        Clean up all expired sessions (not just orphaned ones)
 *   --fix-mismatches  Detect and fix token mismatches
 */

const { createClient } = require('redis');

// Simple logger for scripts (since we can't easily import ES modules in Node.js scripts)
const logger = {
  info: (message, ...args) => console.log(message, ...args),
  warn: (message, ...args) => console.warn(message, ...args),
  error: (message, ...args) => console.error(message, ...args),
};

// Redis configuration
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const REDIS_SESSION_PREFIX = 'session:';
const REDIS_COGNITO_PREFIX = 'cognito:';

// Command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const cleanAll = args.includes('--all');
const fixMismatches = args.includes('--fix-mismatches');

async function cleanupSessions() {
  const redis = createClient({
    url: REDIS_URL,
  });

  try {
    await redis.connect();
    console.log('✅ Connected to Redis');

    // Get all session keys
    const sessionKeys = await redis.keys(`${REDIS_SESSION_PREFIX}*`);
    console.log(`🔍 Found ${sessionKeys.length} session keys in Redis`);

    if (sessionKeys.length === 0) {
      console.log('✨ No sessions found in Redis');
      return;
    }

    let expiredCount = 0;
    let mismatchCount = 0;
    let fixedMismatchCount = 0;
    let totalSize = 0;

    for (const key of sessionKeys) {
      try {
        const data = await redis.get(key);
        if (!data) continue;

        totalSize += data.length;
        const sessionData = JSON.parse(data);
        const sessionId = key.replace(REDIS_SESSION_PREFIX, '');

        // Check if session is expired
        const isExpired = sessionData.exp && Date.now() / 1000 > sessionData.exp;

        if (isExpired || cleanAll) {
          expiredCount++;

          if (isDryRun) {
            console.log(
              `🗑️  [DRY RUN] Would delete expired session: ${sessionId.substring(0, 10)}... (user: ${sessionData.email || 'unknown'})`,
            );
          } else {
            // Delete session and associated cognito data
            const cognitoKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
            await Promise.all([redis.del(key), redis.del(cognitoKey)]);

            console.log(
              `🗑️  Deleted expired session: ${sessionId.substring(0, 10)}... (user: ${sessionData.email || 'unknown'})`,
            );
          }
        } else if (fixMismatches) {
          // Check for token mismatch
          const cognitoKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
          const tokenData = await redis.get(cognitoKey);

          if (!tokenData) {
            mismatchCount++;
            console.warn(
              `⚠️  Token mismatch detected: session ${sessionId.substring(0, 10)}... has no Cognito tokens`,
            );

            if (isDryRun) {
              console.log(
                `🔧 [DRY RUN] Would fix token mismatch for session: ${sessionId.substring(0, 10)}...`,
              );
            } else {
              // In a real scenario, you might want to invalidate the session
              // or attempt token recovery from other sources
              console.log(
                `🗑️  Cleaning up session with missing tokens: ${sessionId.substring(0, 10)}...`,
              );
              await redis.del(key);
              fixedMismatchCount++;
            }
          } else {
            // Verify token data is valid
            try {
              const tokens = JSON.parse(tokenData);
              if (!tokens.accessToken && !tokens.idToken) {
                mismatchCount++;
                console.warn(
                  `⚠️  Invalid token data for session: ${sessionId.substring(0, 10)}... (empty tokens)`,
                );

                if (!isDryRun) {
                  await redis.del(cognitoKey);
                  await redis.del(key);
                  fixedMismatchCount++;
                }
              }
            } catch (parseError) {
              mismatchCount++;
              console.warn(
                `⚠️  Corrupted token data for session: ${sessionId.substring(0, 10)}...`,
              );

              if (!isDryRun) {
                await redis.del(cognitoKey);
                await redis.del(key);
                fixedMismatchCount++;
              }
            }
          }
        } else {
          const timeLeft = Math.round((sessionData.exp * 1000 - Date.now()) / (1000 * 60 * 60));
          console.log(
            `✅ Active session: ${sessionId.substring(0, 10)}... (user: ${sessionData.email || 'unknown'}, expires in ${timeLeft}h)`,
          );
        }
      } catch (error) {
        console.warn(`⚠️  Error processing session key ${key}:`, error.message);
      }
    }

    // Summary
    console.log('\n📊 Cleanup Summary:');
    console.log(`   Total sessions found: ${sessionKeys.length}`);
    console.log(`   Expired sessions ${isDryRun ? 'that would be' : ''} cleaned: ${expiredCount}`);

    if (fixMismatches) {
      console.log(`   Token mismatches detected: ${mismatchCount}`);
      console.log(`   Mismatches ${isDryRun ? 'that would be' : ''} fixed: ${fixedMismatchCount}`);
    }

    console.log(`   Total data size: ${(totalSize / 1024).toFixed(2)} KB`);

    if (isDryRun) {
      console.log('\n💡 Run without --dry-run to actually perform cleanup');
    }

    if (fixMismatches && mismatchCount > 0) {
      console.log('\n⚠️  Token mismatches found! Consider investigating why tokens are missing.');
      console.log('   Common causes:');
      console.log('   - Manual deletion of cognito:* keys');
      console.log('   - Different TTL settings for sessions vs tokens');
      console.log('   - Redis storage issues or restarts');
    }
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  } finally {
    await redis.disconnect();
  }
}

// Run the cleanup
if (require.main === module) {
  logger.info('🧹 Starting Redis session cleanup...');
  if (isDryRun) {
    logger.info('🔍 DRY RUN MODE - No data will be deleted');
  }
  if (cleanAll) {
    logger.info('⚠️  ALL MODE - Will clean up all sessions (not just expired)');
  }
  cleanupSessions().catch(logger.error);
}

module.exports = { cleanupSessions };
