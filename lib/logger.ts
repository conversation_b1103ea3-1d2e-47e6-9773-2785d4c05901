// logger.ts
import { LogLayer } from 'loglayer';
import { PinoTransport } from '@loglayer/transport-pino';
import { getSimplePrettyTerminal } from '@loglayer/transport-simple-pretty-terminal';
import { serializeError } from 'serialize-error';
import { pino } from 'pino';

// Detect if we're on the server or client
const isServer = typeof window === 'undefined';

// Create a Pino instance (only needs to be done once)
const pinoLogger = pino({
  level: 'trace', // Set to desired log level
});

const log = new LogLayer({
  errorSerializer: serializeError,
  transport: [
    // Simple Pretty Terminal for development
    getSimplePrettyTerminal({
      enabled: process.env.NODE_ENV === 'development',
      runtime: isServer ? 'node' : 'browser',
      viewMode: 'inline',
    }),
    // Pino for production (both server and client)
    new PinoTransport({
      enabled: process.env.NODE_ENV === 'production',
      logger: pinoLogger,
    }),
  ],
});

export function getLogger() {
  return log;
}
