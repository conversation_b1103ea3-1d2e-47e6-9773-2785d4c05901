import Redis, { RedisOptions } from 'ioredis';
import { getLogger } from './logger';

let redis: Redis | null = null;

/**
 * Get Redis configuration with security and performance optimizations
 */
function getRedisConfig(): RedisOptions {
  // Use server-side environment variable (not NEXT_PUBLIC_)
  const redisUrl =
    process.env.REDIS_URL || process.env.NEXT_PUBLIC_REDIS_URL || 'redis://localhost:6379';

  const baseConfig: RedisOptions = {
    // Connection settings optimized for stability
    connectTimeout: 5000, // 5 seconds for initial connection
    commandTimeout: 3000, // 3 seconds for commands
    lazyConnect: false, // Connect immediately to avoid lazy connection issues

    // Retry strategy with more conservative settings
    retryDelayOnFailover: 200,
    maxRetriesPerRequest: 2, // Reduced from 3 to avoid long retry cycles
    retryDelayOnClusterDown: 300,

    // Connection pool settings for stability
    family: 4, // Use IPv4
    keepAlive: 60000, // 60 seconds keep-alive

    // Connection name for debugging and monitoring
    connectionName: `partner-dashboard-${process.env.NODE_ENV || 'development'}`,

    // Performance optimizations
    enableReadyCheck: true,
    maxLoadingTimeout: 3000, // Reduced timeout

    // Disable auto-reconnect on certain errors to prevent connection loops
    enableAutoPipelining: false,

    // Security settings
    tls: process.env.NODE_ENV === 'production' && !redisUrl.includes('localhost') ? {} : undefined,
  };

  // Add authentication if provided
  if (process.env.REDIS_PASSWORD) {
    baseConfig.password = process.env.REDIS_PASSWORD;
  }

  // Add username if provided (Redis 6+ ACL support)
  if (process.env.REDIS_USERNAME) {
    baseConfig.username = process.env.REDIS_USERNAME;
  }

  return baseConfig;
}

/**
 * Get or create Redis client instance with improved stability
 * Implements singleton pattern for connection reuse
 */
export function getRedisClient(): Redis {
  // Return existing connection if it's ready
  if (redis && (redis.status === 'ready' || redis.status === 'connecting')) {
    return redis;
  }

  // Clean up any existing broken connection
  if (redis && redis.status === 'end') {
    redis = null;
  }

  const redisUrl =
    process.env.REDIS_URL || process.env.NEXT_PUBLIC_REDIS_URL || 'redis://localhost:6379';
  const config = getRedisConfig();

  console.log(
    '🔌 [REDIS] Creating new Redis connection:',
    redisUrl.replace(/\/\/.*@/, '//***:***@'),
  );

  redis = new Redis(redisUrl, config);

  // Enhanced connection event handlers with detailed logging
  const logger = getLogger();
  redis.on('connect', () => {
    logger.info('✅ [REDIS] Connected to Redis server');
  });

  redis.on('ready', () => {
    logger.info('🚀 [REDIS] Redis client ready');
    // Log Redis server info in development
    if (process.env.NODE_ENV === 'development') {
      redis
        .info('server')
        .then((info) => {
          const version = info.match(/redis_version:([^\r\n]+)/)?.[1];
          logger.info(`📊 [REDIS] Server version: ${version}`);
        })
        .catch(() => {
          // Ignore info errors in development
        });
    }
  });

  redis.on('error', (error) => {
    const errorCode = (error as any).code;

    // Handle specific connection reset errors
    if (errorCode === 'ECONNRESET' || errorCode === 'EPIPE') {
      logger.warn('⚠️ [REDIS] Connection reset detected, will reconnect:', {
        message: error.message,
        code: errorCode,
        timestamp: new Date().toISOString(),
      });
    } else {
      logger.error('❌ [REDIS] Redis connection error:', {
        message: error.message,
        code: errorCode,
        errno: (error as any).errno,
        timestamp: new Date().toISOString(),
      });
    }
  });

  redis.on('close', () => {
    logger.info('🔌 [REDIS] Redis connection closed');
  });

  redis.on('reconnecting', (delay) => {
    logger.info(`🔄 [REDIS] Redis reconnecting in ${delay}ms...`);
  });

  redis.on('end', () => {
    console.log('🔚 [REDIS] Redis connection ended');
  });

  // Handle connection failures gracefully
  redis.on('node error', (error, node) => {
    console.error('❌ [REDIS] Node error:', {
      error: error.message,
      node: node.options.host + ':' + node.options.port,
      timestamp: new Date().toISOString(),
    });
  });

  return redis;
}

/**
 * Health check for Redis connection with improved error handling
 * Returns true if Redis is available, false otherwise
 */
export async function isRedisHealthy(): Promise<boolean> {
  try {
    const client = getRedisClient();

    // Check connection status first
    if (client.status !== 'ready' && client.status !== 'connecting') {
      console.warn('⚠️ [REDIS] Client not ready, status:', client.status);
      return false;
    }

    // Wait for connection if it's still connecting
    if (client.status === 'connecting') {
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error('Connection timeout')), 3000);
        client.once('ready', () => {
          clearTimeout(timeout);
          resolve(true);
        });
        client.once('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });
    }

    // Perform ping test with timeout
    const pingPromise = client.ping();
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Ping timeout')), 2000);
    });

    const pingResult = await Promise.race([pingPromise, timeoutPromise]);
    if (pingResult !== 'PONG') {
      console.warn('⚠️ [REDIS] Ping returned unexpected result:', pingResult);
      return false;
    }

    // Test basic operations with shorter timeout
    const testKey = `health:${Date.now()}:${Math.random().toString(36).substr(2, 5)}`;
    await client.set(testKey, 'test', 'EX', 1); // 1 second expiry
    const value = await client.get(testKey);
    await client.del(testKey);

    if (value !== 'test') {
      console.warn('⚠️ [REDIS] Basic operations test failed');
      return false;
    }

    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorCode = (error as any)?.code;

    // Don't log connection reset errors as errors (they're expected during reconnection)
    if (errorCode === 'ECONNRESET' || errorCode === 'EPIPE') {
      console.debug(
        '🔄 [REDIS] Health check failed due to connection reset (normal during reconnection)',
      );
    } else {
      console.warn('⚠️ [REDIS] Health check failed:', {
        error: errorMessage,
        code: errorCode,
        timestamp: new Date().toISOString(),
      });
    }
    return false;
  }
}

/**
 * Gracefully disconnect from Redis with timeout
 * Used during application shutdown
 */
export async function disconnectRedis(timeoutMs: number = 5000): Promise<void> {
  if (redis) {
    try {
      // Set a timeout for the disconnect operation
      const disconnectPromise = redis.quit();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Disconnect timeout')), timeoutMs);
      });

      await Promise.race([disconnectPromise, timeoutPromise]);
      console.log('👋 [REDIS] Gracefully disconnected');
    } catch (error) {
      console.error('❌ [REDIS] Error during disconnect:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });

      // Force disconnect if graceful disconnect fails
      try {
        redis.disconnect();
        console.log('🔌 [REDIS] Force disconnected');
      } catch (forceError) {
        console.error('❌ [REDIS] Force disconnect failed:', forceError);
      }
    } finally {
      redis = null;
    }
  }
}

/**
 * Get Redis connection status and statistics
 */
export function getRedisStatus(): {
  connected: boolean;
  status: string;
  connectionName?: string;
  uptime?: number;
} {
  if (!redis) {
    return { connected: false, status: 'not_initialized' };
  }

  return {
    connected: redis.status === 'ready',
    status: redis.status,
    connectionName: redis.options.connectionName,
    uptime: redis.connector?.connecting ? undefined : Date.now() - (redis as any).connectTime,
  };
}

/**
 * Comprehensive Redis connectivity and performance test
 * Useful for debugging and health checks
 */
export async function testRedisConnection(): Promise<{
  connected: boolean;
  error?: string;
  latency?: number;
  operations?: {
    ping: number;
    set: number;
    get: number;
    del: number;
  };
  serverInfo?: {
    version?: string;
    mode?: string;
    role?: string;
    connectedClients?: number;
    usedMemory?: string;
  };
}> {
  const startTime = Date.now();
  const operations = { ping: 0, set: 0, get: 0, del: 0 };

  try {
    const client = getRedisClient();

    // Test ping operation
    const pingStart = Date.now();
    const pingResult = await client.ping();
    operations.ping = Date.now() - pingStart;

    if (pingResult !== 'PONG') {
      return {
        connected: false,
        error: `Ping returned unexpected result: ${pingResult}`,
        latency: Date.now() - startTime,
        operations,
      };
    }

    // Test basic operations with performance tracking
    const testKey = `test:connection:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
    const testValue = `test-value-${Date.now()}`;

    // SET operation
    const setStart = Date.now();
    await client.set(testKey, testValue, 'EX', 10); // 10 second expiry
    operations.set = Date.now() - setStart;

    // GET operation
    const getStart = Date.now();
    const value = await client.get(testKey);
    operations.get = Date.now() - getStart;

    // DEL operation
    const delStart = Date.now();
    await client.del(testKey);
    operations.del = Date.now() - delStart;

    const totalLatency = Date.now() - startTime;

    if (value !== testValue) {
      return {
        connected: false,
        error: 'Set/get operation failed - value mismatch',
        latency: totalLatency,
        operations,
      };
    }

    // Get server information (optional, may fail in restricted environments)
    let serverInfo: any = {};
    try {
      const info = await client.info('server');
      const memory = await client.info('memory');
      const clients = await client.info('clients');

      serverInfo = {
        version: info.match(/redis_version:([^\r\n]+)/)?.[1],
        mode: info.match(/redis_mode:([^\r\n]+)/)?.[1],
        role: info.match(/role:([^\r\n]+)/)?.[1],
        connectedClients: parseInt(clients.match(/connected_clients:(\d+)/)?.[1] || '0'),
        usedMemory: memory.match(/used_memory_human:([^\r\n]+)/)?.[1],
      };
    } catch (infoError) {
      // Server info is optional, don't fail the test
      console.debug('📊 [REDIS] Could not retrieve server info:', infoError);
    }

    return {
      connected: true,
      latency: totalLatency,
      operations,
      serverInfo: Object.keys(serverInfo).length > 0 ? serverInfo : undefined,
    };
  } catch (error) {
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      latency: Date.now() - startTime,
      operations,
    };
  }
}

/**
 * Clear all keys with a specific pattern (use with caution)
 * Useful for development and testing
 */
export async function clearRedisPattern(pattern: string): Promise<number> {
  try {
    const client = getRedisClient();
    const keys = await client.keys(pattern);

    if (keys.length === 0) {
      console.log(`🧹 [REDIS] No keys found matching pattern: ${pattern}`);
      return 0;
    }

    const deleted = await client.del(...keys);
    console.log(`🧹 [REDIS] Deleted ${deleted} keys matching pattern: ${pattern}`);
    return deleted;
  } catch (error) {
    console.error('❌ [REDIS] Failed to clear pattern:', {
      pattern,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
    throw error;
  }
}

/**
 * Get Redis memory usage and key statistics
 */
export async function getRedisStats(): Promise<{
  memory: {
    used: string;
    peak: string;
    fragmentation: number;
  };
  keys: {
    total: number;
    expires: number;
  };
  clients: {
    connected: number;
    blocked: number;
  };
  performance: {
    commandsProcessed: number;
    keyspaceHits: number;
    keyspaceMisses: number;
    hitRate: number;
  };
}> {
  try {
    const client = getRedisClient();

    const [memoryInfo, keyspaceInfo, clientsInfo, statsInfo] = await Promise.all([
      client.info('memory'),
      client.info('keyspace'),
      client.info('clients'),
      client.info('stats'),
    ]);

    // Parse memory information
    const usedMemory = memoryInfo.match(/used_memory_human:([^\r\n]+)/)?.[1] || 'unknown';
    const peakMemory = memoryInfo.match(/used_memory_peak_human:([^\r\n]+)/)?.[1] || 'unknown';
    const fragmentation = parseFloat(
      memoryInfo.match(/mem_fragmentation_ratio:([^\r\n]+)/)?.[1] || '0',
    );

    // Parse keyspace information
    const dbInfo = keyspaceInfo.match(/db0:keys=(\d+),expires=(\d+)/);
    const totalKeys = dbInfo ? parseInt(dbInfo[1]) : 0;
    const expiringKeys = dbInfo ? parseInt(dbInfo[2]) : 0;

    // Parse client information
    const connectedClients = parseInt(clientsInfo.match(/connected_clients:(\d+)/)?.[1] || '0');
    const blockedClients = parseInt(clientsInfo.match(/blocked_clients:(\d+)/)?.[1] || '0');

    // Parse performance statistics
    const commandsProcessed = parseInt(
      statsInfo.match(/total_commands_processed:(\d+)/)?.[1] || '0',
    );
    const keyspaceHits = parseInt(statsInfo.match(/keyspace_hits:(\d+)/)?.[1] || '0');
    const keyspaceMisses = parseInt(statsInfo.match(/keyspace_misses:(\d+)/)?.[1] || '0');
    const hitRate =
      keyspaceHits + keyspaceMisses > 0
        ? (keyspaceHits / (keyspaceHits + keyspaceMisses)) * 100
        : 0;

    return {
      memory: {
        used: usedMemory,
        peak: peakMemory,
        fragmentation,
      },
      keys: {
        total: totalKeys,
        expires: expiringKeys,
      },
      clients: {
        connected: connectedClients,
        blocked: blockedClients,
      },
      performance: {
        commandsProcessed,
        keyspaceHits,
        keyspaceMisses,
        hitRate: Math.round(hitRate * 100) / 100,
      },
    };
  } catch (error) {
    console.error('❌ [REDIS] Failed to get stats:', error);
    throw error;
  }
}

/**
 * Setup graceful shutdown handler for Redis
 * Call this during application startup
 */
export function setupRedisShutdownHandler(): void {
  const gracefulShutdown = async (signal: string) => {
    console.log(`🔄 [REDIS] Received ${signal}, shutting down gracefully...`);
    try {
      await disconnectRedis(3000); // 3 second timeout
      console.log('✅ [REDIS] Shutdown complete');
      process.exit(0);
    } catch (error) {
      console.error('❌ [REDIS] Shutdown error:', error);
      process.exit(1);
    }
  };

  // Handle various shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // Nodemon restart

  // Handle uncaught exceptions
  process.on('uncaughtException', async (error) => {
    console.error('❌ [REDIS] Uncaught exception:', error);
    await disconnectRedis(1000); // Quick shutdown
    process.exit(1);
  });

  process.on('unhandledRejection', async (reason, promise) => {
    console.error('❌ [REDIS] Unhandled rejection at:', promise, 'reason:', reason);
    await disconnectRedis(1000); // Quick shutdown
    process.exit(1);
  });
}
