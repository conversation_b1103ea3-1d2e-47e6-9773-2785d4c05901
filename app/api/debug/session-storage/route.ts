import { NextRequest, NextResponse } from 'next/server';
import {
  createSession,
  verifySession,
  setCognitoTokenCookies,
  getCognitoTokenCookies,
  getCurrentSessionId,
  clearSession,
} from '@/lib/session';
import { isRedisHealthy } from '@/lib/redis';
import { getLogger } from '@/lib/logger';

/**
 * Debug endpoint to test Redis-based session storage
 * GET /api/debug/session-storage
 */
export async function GET(request: NextRequest) {
  // Security: Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Debug endpoints not available in production' },
      { status: 403 },
    );
  }

  const startTime = Date.now();
  const logger = getLogger();
  logger.info('🧪 [DEBUG] Testing Redis-based session storage...');

  try {
    const results = {
      timestamp: new Date().toISOString(),
      redis: {
        available: false,
        healthy: false,
      },
      storageMode: process.env.SESSION_STORAGE_MODE || 'redis',
      tests: {
        sessionCreation: false,
        sessionVerification: false,
        tokenStorage: false,
        tokenRetrieval: false,
        sessionCleanup: false,
      },
      performance: {
        totalTime: 0,
        sessionCreateTime: 0,
        tokenStoreTime: 0,
        tokenRetrieveTime: 0,
      },
      errors: [] as string[],
    };

    // Test Redis availability
    try {
      results.redis.healthy = await isRedisHealthy();
      results.redis.available = true;
      logger.info('✅ [DEBUG] Redis is available and healthy');
    } catch (error) {
      results.errors.push(`Redis health check failed: ${error}`);
      logger.warn('⚠️ [DEBUG] Redis not available, will test fallback mode');
    }

    // Test 1: Session Creation
    console.log('🧪 [DEBUG] Test 1: Creating session...');
    const sessionStart = Date.now();

    const testUserData = {
      username: '<EMAIL>',
      email: '<EMAIL>',
      userId: 'test-user-123',
    };

    let sessionToken: string;
    try {
      sessionToken = await createSession(testUserData);
      results.tests.sessionCreation = true;
      results.performance.sessionCreateTime = Date.now() - sessionStart;
      console.log(`✅ [DEBUG] Session created: ${sessionToken.substring(0, 20)}...`);
    } catch (error) {
      results.errors.push(`Session creation failed: ${error}`);
      throw error;
    }

    // Test 2: Session Verification
    console.log('🧪 [DEBUG] Test 2: Verifying session...');
    try {
      const sessionData = await verifySession(sessionToken);
      if (sessionData && sessionData.email === testUserData.email) {
        results.tests.sessionVerification = true;
        console.log('✅ [DEBUG] Session verification successful');
      } else {
        results.errors.push('Session verification returned invalid data');
      }
    } catch (error) {
      results.errors.push(`Session verification failed: ${error}`);
    }

    // Test 3: Token Storage
    console.log('🧪 [DEBUG] Test 3: Storing Cognito tokens...');
    const tokenStoreStart = Date.now();

    const testTokens = {
      accessToken: 'test-access-token-' + Date.now(),
      idToken: 'test-id-token-' + Date.now(),
    };

    try {
      await setCognitoTokenCookies(testTokens, sessionToken);
      results.tests.tokenStorage = true;
      results.performance.tokenStoreTime = Date.now() - tokenStoreStart;
      console.log('✅ [DEBUG] Cognito tokens stored successfully');
    } catch (error) {
      results.errors.push(`Token storage failed: ${error}`);
    }

    // Test 4: Token Retrieval
    console.log('🧪 [DEBUG] Test 4: Retrieving Cognito tokens...');
    const tokenRetrieveStart = Date.now();

    try {
      const retrievedTokens = await getCognitoTokenCookies(sessionToken);
      if (
        retrievedTokens.accessToken === testTokens.accessToken &&
        retrievedTokens.idToken === testTokens.idToken
      ) {
        results.tests.tokenRetrieval = true;
        results.performance.tokenRetrieveTime = Date.now() - tokenRetrieveStart;
        console.log('✅ [DEBUG] Token retrieval successful');
      } else {
        results.errors.push('Token retrieval returned different tokens');
      }
    } catch (error) {
      results.errors.push(`Token retrieval failed: ${error}`);
    }

    // Test 5: Session Cleanup
    console.log('🧪 [DEBUG] Test 5: Cleaning up session...');
    console.log(`🧪 [DEBUG] Session token to clean: ${sessionToken}`);
    try {
      // In Redis mode, sessionToken is the session ID
      // In JWT mode, we need to extract the session ID from the JWT
      await clearSession(sessionToken);

      // Wait a moment for Redis cleanup to complete
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Verify cleanup by trying to verify the session again
      console.log('🧪 [DEBUG] Verifying session after cleanup...');
      const cleanedSession = await verifySession(sessionToken);

      if (!cleanedSession) {
        results.tests.sessionCleanup = true;
        console.log('✅ [DEBUG] Session cleanup successful - session no longer exists');
      } else {
        results.errors.push('Session still exists after cleanup');
        console.log('⚠️ [DEBUG] Session still exists after cleanup:', {
          sessionId: sessionToken,
          sessionData: cleanedSession,
          redisHealthy: await isRedisHealthy(),
        });
      }
    } catch (error) {
      results.errors.push(`Session cleanup failed: ${error}`);
      console.error('❌ [DEBUG] Session cleanup error:', error);
    }

    results.performance.totalTime = Date.now() - startTime;

    // Summary
    const passedTests = Object.values(results.tests).filter(Boolean).length;
    const totalTests = Object.keys(results.tests).length;

    console.log(
      `🧪 [DEBUG] Session storage test completed: ${passedTests}/${totalTests} tests passed`,
    );

    return NextResponse.json(
      {
        ...results,
        summary: {
          passed: passedTests,
          total: totalTests,
          success: passedTests === totalTests && results.errors.length === 0,
          recommendation: results.redis.healthy
            ? 'Redis-based session storage is working correctly'
            : 'Redis not available, using cookie fallback mode',
        },
      },
      {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Content-Type': 'application/json',
        },
      },
    );
  } catch (error) {
    logger.error('❌ [DEBUG] Session storage test failed:', error);

    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false,
        duration: Date.now() - startTime,
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Content-Type': 'application/json',
        },
      },
    );
  }
}

/**
 * Clear all test sessions (development only)
 * DELETE /api/debug/session-storage
 */
export async function DELETE(request: NextRequest) {
  // Security: Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Debug endpoints not available in production' },
      { status: 403 },
    );
  }

  try {
    // This would clear all test sessions if we had a way to list them
    // For now, just return success
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      message: 'Test session cleanup completed',
      success: true,
    });
  } catch (error) {
    console.error('❌ [DEBUG] Failed to clear test sessions:', error);

    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false,
      },
      { status: 500 },
    );
  }
}
